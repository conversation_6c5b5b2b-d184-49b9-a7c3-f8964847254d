#!/bin/bash

echo "=== TEST DE LA CORRECTION API DESTROY ==="

# Attendre que le serveur démarre
sleep 3

# 1. Test de connexion avec l'utilisateur qui a des ServiceOffers (<EMAIL> - ID 5)
echo "1. Connexion avec l'utilisateur <EMAIL>"
LOGIN_RESPONSE=$(curl -s -X POST http://127.0.0.1:8000/api/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }')

echo "Réponse de connexion: $LOGIN_RESPONSE"

# Extraire le token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "Token extrait: $TOKEN"

if [ -z "$TOKEN" ]; then
    echo "❌ ERREUR: Impossible d'extraire le token"
    exit 1
fi

# 2. Récupérer les service offers de l'utilisateur
echo ""
echo "2. Récupération des service offers de l'utilisateur"
OFFERS_RESPONSE=$(curl -s -X GET http://127.0.0.1:8000/api/service-offers \
  -H "Authorization: Bearer $TOKEN" \
  -H "Accept: application/json")

echo "Service offers: $OFFERS_RESPONSE"

# 3. Test de suppression d'une service offer qui appartient à l'utilisateur (ID 2 ou 3)
echo ""
echo "3. Test de suppression de la service offer ID 2 (appartient à l'utilisateur 5)"
DELETE_RESPONSE=$(curl -s -w "HTTP_STATUS:%{http_code}" -X DELETE http://127.0.0.1:8000/api/service-offers/2 \
  -H "Authorization: Bearer $TOKEN" \
  -H "Accept: application/json")

HTTP_STATUS=$(echo $DELETE_RESPONSE | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo $DELETE_RESPONSE | sed 's/HTTP_STATUS:[0-9]*$//')

echo "Status Code: $HTTP_STATUS"
echo "Réponse: $RESPONSE_BODY"

if [ "$HTTP_STATUS" = "204" ] || [ "$HTTP_STATUS" = "200" ]; then
    echo "✅ SUCCÈS: La suppression a fonctionné!"
elif [ "$HTTP_STATUS" = "403" ]; then
    echo "❌ ÉCHEC: Accès refusé - le problème persiste"
elif [ "$HTTP_STATUS" = "401" ]; then
    echo "❌ ÉCHEC: Non authentifié - problème d'authentification"
else
    echo "❌ ÉCHEC: Status inattendu $HTTP_STATUS"
fi

# 4. Test avec un autre utilisateur pour vérifier que l'autorisation fonctionne
echo ""
echo "4. Test avec un autre utilisateur (<EMAIL> - ID 4)"
OTHER_LOGIN_RESPONSE=$(curl -s -X POST http://127.0.0.1:8000/api/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }')

OTHER_TOKEN=$(echo $OTHER_LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "Token autre utilisateur: $OTHER_TOKEN"

if [ ! -z "$OTHER_TOKEN" ]; then
    echo "Test de suppression non autorisée de l'offre ID 3 (appartient à l'utilisateur 5)"
    UNAUTHORIZED_DELETE=$(curl -s -w "HTTP_STATUS:%{http_code}" -X DELETE http://127.0.0.1:8000/api/service-offers/3 \
      -H "Authorization: Bearer $OTHER_TOKEN" \
      -H "Accept: application/json")
    
    UNAUTH_STATUS=$(echo $UNAUTHORIZED_DELETE | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
    UNAUTH_BODY=$(echo $UNAUTHORIZED_DELETE | sed 's/HTTP_STATUS:[0-9]*$//')
    
    echo "Status Code: $UNAUTH_STATUS"
    echo "Réponse: $UNAUTH_BODY"
    
    if [ "$UNAUTH_STATUS" = "403" ]; then
        echo "✅ SUCCÈS: L'accès non autorisé a été correctement bloqué!"
    else
        echo "❌ ÉCHEC: L'accès non autorisé n'a pas été bloqué (Status: $UNAUTH_STATUS)"
    fi
fi

echo ""
echo "=== FIN DU TEST ==="
echo ""
echo "Vérifiez les logs dans storage/logs/laravel.log pour plus de détails."
