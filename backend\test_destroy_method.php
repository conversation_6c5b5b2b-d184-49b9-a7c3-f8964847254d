<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\ServiceOffer;
use App\Models\User;
use App\Http\Controllers\Api\ServiceOfferController;
use Illuminate\Support\Facades\Auth;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Create a fake request to bootstrap the application
$request = Request::create('/', 'GET');
$response = $kernel->handle($request);

echo "=== TEST DESTROY METHOD ===\n\n";

// 1. Récupérer les données
$serviceOffer = ServiceOffer::first();
$user = User::find($serviceOffer->user_id);

echo "1. Données de test:\n";
echo "  - ServiceOffer ID: {$serviceOffer->id}\n";
echo "  - ServiceOffer user_id: {$serviceOffer->user_id}\n";
echo "  - User: {$user->first_name} {$user->last_name}\n\n";

// 2. Test sans authentification
echo "2. Test sans authentification:\n";
$controller = new ServiceOfferController();
try {
    $response = $controller->destroy($serviceOffer);
    echo "  - Status: {$response->getStatusCode()}\n";
    echo "  - Content: {$response->getContent()}\n";
} catch (\Exception $e) {
    echo "  - Erreur: {$e->getMessage()}\n";
}
echo "\n";

// 3. Test avec authentification du propriétaire
echo "3. Test avec authentification du propriétaire:\n";
Auth::login($user);
try {
    $response = $controller->destroy($serviceOffer);
    echo "  - Status: {$response->getStatusCode()}\n";
    echo "  - Content: {$response->getContent()}\n";
} catch (\Exception $e) {
    echo "  - Erreur: {$e->getMessage()}\n";
}
Auth::logout();
echo "\n";

// 4. Test avec authentification d'un autre utilisateur
echo "4. Test avec authentification d'un autre utilisateur:\n";
$otherUser = User::where('id', '!=', $serviceOffer->user_id)->first();
if ($otherUser) {
    Auth::login($otherUser);
    echo "  - Autre utilisateur: {$otherUser->first_name} {$otherUser->last_name} (ID: {$otherUser->id})\n";
    try {
        $response = $controller->destroy($serviceOffer);
        echo "  - Status: {$response->getStatusCode()}\n";
        echo "  - Content: {$response->getContent()}\n";
    } catch (\Exception $e) {
        echo "  - Erreur: {$e->getMessage()}\n";
    }
    Auth::logout();
}

echo "\n=== FIN DU TEST ===\n";
