#!/bin/bash

echo "=== TEST CURL API ==="

# 1. Test de connexion
echo "1. Test de connexion à l'utilisateur <EMAIL>"
LOGIN_RESPONSE=$(curl -s -X POST http://127.0.0.1:8000/api/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }')

echo "Réponse de connexion: $LOGIN_RESPONSE"

# Extraire le token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "Token extrait: $TOKEN"

if [ -z "$TOKEN" ]; then
    echo "Erreur: Impossible d'extraire le token"
    exit 1
fi

# 2. Test de récupération des service offers
echo ""
echo "2. Récupération des service offers"
OFFERS_RESPONSE=$(curl -s -X GET http://127.0.0.1:8000/api/service-offers \
  -H "Authorization: Bearer $TOKEN" \
  -H "Accept: application/json")

echo "Service offers: $OFFERS_RESPONSE"

# 3. Test de suppression d'une service offer (ID 2 ou 3 appartiennent à l'utilisateur 5)
echo ""
echo "3. Test de suppression de la service offer ID 2"
DELETE_RESPONSE=$(curl -s -X DELETE http://127.0.0.1:8000/api/service-offers/2 \
  -H "Authorization: Bearer $TOKEN" \
  -H "Accept: application/json")

echo "Réponse de suppression: $DELETE_RESPONSE"

# 4. Test de suppression d'une service offer qui n'appartient pas à l'utilisateur (ID 1 appartient à l'utilisateur 4)
echo ""
echo "4. Test de suppression de la service offer ID 1 (non autorisé)"
DELETE_RESPONSE_UNAUTHORIZED=$(curl -s -X DELETE http://127.0.0.1:8000/api/service-offers/1 \
  -H "Authorization: Bearer $TOKEN" \
  -H "Accept: application/json")

echo "Réponse de suppression non autorisée: $DELETE_RESPONSE_UNAUTHORIZED"

echo ""
echo "=== FIN DU TEST ==="
