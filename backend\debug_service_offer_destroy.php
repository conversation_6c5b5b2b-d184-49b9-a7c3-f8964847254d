<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\ServiceOffer;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Laravel\Sanctum\PersonalAccessToken;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Create a fake request to bootstrap the application
$request = Request::create('/', 'GET');
$response = $kernel->handle($request);

echo "=== DIAGNOSTIC ServiceOffer Destroy Method ===\n\n";

// 1. Vérifier les ServiceOffers existants
echo "1. ServiceOffers dans la base de données:\n";
$serviceOffers = ServiceOffer::with('user')->get();
foreach ($serviceOffers as $offer) {
    echo "  - ID: {$offer->id}, Title: {$offer->title}, User ID: {$offer->user_id}, User: {$offer->user->first_name} {$offer->user->last_name}\n";
}
echo "\n";

// 2. Vérifier les utilisateurs
echo "2. Utilisateurs dans la base de données:\n";
$users = User::all();
foreach ($users as $user) {
    echo "  - ID: {$user->id}, Name: {$user->first_name} {$user->last_name}, Email: {$user->email}\n";
}
echo "\n";

// 3. Vérifier les tokens Sanctum
echo "3. Tokens Sanctum dans la base de données:\n";
$tokens = PersonalAccessToken::with('tokenable')->get();
foreach ($tokens as $token) {
    echo "  - Token ID: {$token->id}, User ID: {$token->tokenable_id}, Name: {$token->name}\n";
}
echo "\n";

// 4. Simuler une authentification et test de suppression
if ($serviceOffers->count() > 0 && $users->count() > 0) {
    $testOffer = $serviceOffers->first();
    $owner = User::find($testOffer->user_id);

    echo "4. Test de suppression avec authentification simulée:\n";
    echo "  - ServiceOffer ID: {$testOffer->id}, user_id: {$testOffer->user_id}\n";
    echo "  - Propriétaire: {$owner->first_name} {$owner->last_name} (ID: {$owner->id})\n";

    // Simuler l'authentification avec le propriétaire
    Auth::login($owner);
    echo "  - Utilisateur authentifié: " . (Auth::check() ? 'Oui' : 'Non') . "\n";
    echo "  - ID utilisateur authentifié: " . (Auth::id() ?? 'null') . "\n";
    echo "  - Comparaison: {$testOffer->user_id} !== " . Auth::id() . " = " . ($testOffer->user_id !== Auth::id() ? 'true' : 'false') . "\n";

    // Test avec un autre utilisateur
    $otherUser = $users->where('id', '!=', $testOffer->user_id)->first();
    if ($otherUser) {
        Auth::login($otherUser);
        echo "  - Test avec autre utilisateur (ID: {$otherUser->id}):\n";
        echo "    Comparaison: {$testOffer->user_id} !== " . Auth::id() . " = " . ($testOffer->user_id !== Auth::id() ? 'true' : 'false') . "\n";
    }

    Auth::logout();
}

// 5. Vérifier les types de données
echo "\n5. Vérification des types de données:\n";
if ($serviceOffers->count() > 0) {
    $testOffer = $serviceOffers->first();
    echo "  - Type de user_id dans ServiceOffer: " . gettype($testOffer->user_id) . " (valeur: {$testOffer->user_id})\n";

    if ($users->count() > 0) {
        $testUser = $users->first();
        echo "  - Type de id dans User: " . gettype($testUser->id) . " (valeur: {$testUser->id})\n";
    }
}

// 6. Vérifier la configuration d'authentification
echo "\n6. Configuration d'authentification:\n";
echo "  - Guard par défaut: " . config('auth.defaults.guard') . "\n";
echo "  - Provider par défaut: " . config('auth.defaults.provider') . "\n";
echo "  - Driver du provider: " . config('auth.providers.users.driver') . "\n";
echo "  - Model du provider: " . config('auth.providers.users.model') . "\n";

echo "\n=== FIN DU DIAGNOSTIC ===\n";
