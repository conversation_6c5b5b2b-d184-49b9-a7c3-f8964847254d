<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\ServiceOffer;
use App\Models\User;
use Laravel\Sanctum\PersonalAccessToken;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "=== TEST DE LA CORRECTION DESTROY ===\n\n";

// 1. Créer un utilisateur et un token
$user = User::find(5); // Utilisateur qui a des ServiceOffers
if (!$user) {
    echo "Utilisateur non trouvé\n";
    exit(1);
}

// Créer un token pour l'utilisateur
$token = $user->createToken('test-token');
echo "1. Token créé pour l'utilisateur {$user->first_name} {$user->last_name} (ID: {$user->id})\n";
echo "   Token: " . substr($token->plainTextToken, 0, 20) . "...\n\n";

// 2. Récupérer une ServiceOffer de cet utilisateur
$serviceOffer = ServiceOffer::where('user_id', $user->id)->first();
if (!$serviceOffer) {
    echo "Aucune ServiceOffer trouvée pour cet utilisateur\n";
    exit(1);
}

echo "2. ServiceOffer trouvée:\n";
echo "   ID: {$serviceOffer->id}\n";
echo "   Title: {$serviceOffer->title}\n";
echo "   User ID: {$serviceOffer->user_id}\n\n";

// 3. Créer une requête DELETE simulée avec le token
$request = Request::create(
    "/api/service-offers/{$serviceOffer->id}",
    'DELETE',
    [],
    [],
    [],
    [
        'HTTP_AUTHORIZATION' => 'Bearer ' . $token->plainTextToken,
        'HTTP_ACCEPT' => 'application/json',
        'HTTP_CONTENT_TYPE' => 'application/json',
    ]
);

echo "3. Test de suppression avec le bon utilisateur:\n";
echo "   URL: {$request->getUri()}\n";
echo "   Method: {$request->getMethod()}\n";
echo "   Authorization: Bearer " . substr($token->plainTextToken, 0, 20) . "...\n\n";

// 4. Traiter la requête
echo "4. Résultat de la requête:\n";
try {
    $response = $kernel->handle($request);
    echo "   Status Code: {$response->getStatusCode()}\n";
    echo "   Content: {$response->getContent()}\n\n";
    
    if ($response->getStatusCode() === 204) {
        echo "✅ SUCCÈS: La suppression a fonctionné!\n";
    } else {
        echo "❌ ÉCHEC: La suppression a échoué\n";
    }
} catch (\Exception $e) {
    echo "   ❌ ERREUR: {$e->getMessage()}\n";
    echo "   Trace: " . substr($e->getTraceAsString(), 0, 500) . "...\n";
}

// 5. Test avec un utilisateur non autorisé
echo "\n5. Test avec un utilisateur non autorisé:\n";
$otherUser = User::where('id', '!=', $user->id)->first();
if ($otherUser) {
    $otherToken = $otherUser->createToken('test-token-other');
    
    // Récupérer une autre ServiceOffer (qui appartient à l'utilisateur original)
    $anotherOffer = ServiceOffer::where('user_id', $user->id)->skip(1)->first();
    if (!$anotherOffer) {
        // Créer une nouvelle ServiceOffer pour le test
        $anotherOffer = ServiceOffer::create([
            'user_id' => $user->id,
            'title' => 'Test Offer for Deletion',
            'description' => 'Test description',
            'price' => 100,
            'categories' => ['test'],
            'execution_time' => '1 week',
            'concepts' => 'Test concepts',
            'revisions' => '3',
            'status' => 'published'
        ]);
    }
    
    $unauthorizedRequest = Request::create(
        "/api/service-offers/{$anotherOffer->id}",
        'DELETE',
        [],
        [],
        [],
        [
            'HTTP_AUTHORIZATION' => 'Bearer ' . $otherToken->plainTextToken,
            'HTTP_ACCEPT' => 'application/json',
            'HTTP_CONTENT_TYPE' => 'application/json',
        ]
    );
    
    echo "   Utilisateur non autorisé: {$otherUser->first_name} {$otherUser->last_name} (ID: {$otherUser->id})\n";
    echo "   Tentative de suppression de l'offre ID: {$anotherOffer->id} (appartient à l'utilisateur {$anotherOffer->user_id})\n";
    
    try {
        $unauthorizedResponse = $kernel->handle($unauthorizedRequest);
        echo "   Status Code: {$unauthorizedResponse->getStatusCode()}\n";
        echo "   Content: {$unauthorizedResponse->getContent()}\n";
        
        if ($unauthorizedResponse->getStatusCode() === 403) {
            echo "   ✅ SUCCÈS: L'accès non autorisé a été correctement bloqué!\n";
        } else {
            echo "   ❌ ÉCHEC: L'accès non autorisé n'a pas été bloqué\n";
        }
    } catch (\Exception $e) {
        echo "   ❌ ERREUR: {$e->getMessage()}\n";
    }
}

echo "\n=== FIN DU TEST ===\n";
echo "\nVérifiez les logs dans storage/logs/laravel.log pour plus de détails.\n";
