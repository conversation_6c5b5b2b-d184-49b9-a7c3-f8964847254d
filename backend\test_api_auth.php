<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\ServiceOffer;
use App\Models\User;
use Laravel\Sanctum\PersonalAccessToken;
use App\Http\Controllers\Api\ServiceOfferController;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "=== TEST API AUTHENTICATION ===\n\n";

// 1. Récupérer un token valide
$token = PersonalAccessToken::with('tokenable')->first();
if (!$token) {
    echo "Aucun token trouvé dans la base de données.\n";
    exit(1);
}

echo "1. Token trouvé:\n";
echo "  - Token ID: {$token->id}\n";
echo "  - User ID: {$token->tokenable_id}\n";
echo "  - Token: " . substr($token->token, 0, 10) . "...\n\n";

// 2. Récupérer une ServiceOffer
$serviceOffer = ServiceOffer::first();
if (!$serviceOffer) {
    echo "Aucune ServiceOffer trouvée.\n";
    exit(1);
}

echo "2. ServiceOffer trouvée:\n";
echo "  - ID: {$serviceOffer->id}\n";
echo "  - Title: {$serviceOffer->title}\n";
echo "  - User ID: {$serviceOffer->user_id}\n\n";

// 3. Créer une requête simulée avec le token
$request = Request::create(
    "/api/service-offers/{$serviceOffer->id}",
    'DELETE',
    [],
    [],
    [],
    [
        'HTTP_AUTHORIZATION' => 'Bearer ' . $token->plainTextToken ?? 'test-token',
        'HTTP_ACCEPT' => 'application/json',
        'HTTP_CONTENT_TYPE' => 'application/json',
    ]
);

echo "3. Requête créée:\n";
echo "  - URL: {$request->getUri()}\n";
echo "  - Method: {$request->getMethod()}\n";
echo "  - Authorization: " . $request->header('Authorization') . "\n\n";

// 4. Traiter la requête
echo "4. Traitement de la requête:\n";
try {
    $response = $kernel->handle($request);
    echo "  - Status Code: {$response->getStatusCode()}\n";
    echo "  - Content: {$response->getContent()}\n";
} catch (\Exception $e) {
    echo "  - Erreur: {$e->getMessage()}\n";
    echo "  - Trace: {$e->getTraceAsString()}\n";
}

echo "\n=== FIN DU TEST ===\n";
