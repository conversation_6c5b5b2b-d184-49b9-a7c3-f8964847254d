#!/bin/bash

echo "=== TEST SIMPLE DE LA CORRECTION DESTROY ==="

# 1. Créer un utilisateur de test
echo "1. Création d'un utilisateur de test"
REGISTER_RESPONSE=$(curl -s -X POST http://127.0.0.1:8000/api/register \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "first_name": "Test",
    "last_name": "User",
    "email": "<EMAIL>",
    "password": "password",
    "password_confirmation": "password",
    "is_professional": true
  }')

echo "Réponse d'inscription: $REGISTER_RESPONSE"

# 2. Se connecter avec l'utilisateur de test
echo ""
echo "2. Connexion avec l'utilisateur de test"
LOGIN_RESPONSE=$(curl -s -X POST http://127.0.0.1:8000/api/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }')

echo "Réponse de connexion: $LOGIN_RESPONSE"

# Extraire le token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "Token extrait: $TOKEN"

if [ -z "$TOKEN" ]; then
    echo "❌ ERREUR: Impossible d'extraire le token"
    
    # Essayer avec un utilisateur existant
    echo ""
    echo "<NAME_EMAIL>"
    LOGIN_RESPONSE2=$(curl -s -X POST http://127.0.0.1:8000/api/login \
      -H "Content-Type: application/json" \
      -H "Accept: application/json" \
      -d '{
        "email": "<EMAIL>",
        "password": "password"
      }')
    
    echo "Réponse de connexion 2: $LOGIN_RESPONSE2"
    TOKEN=$(echo $LOGIN_RESPONSE2 | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    echo "Token extrait 2: $TOKEN"
fi

if [ -z "$TOKEN" ]; then
    echo "❌ ERREUR: Impossible de se connecter"
    exit 1
fi

# 3. Créer une service offer
echo ""
echo "3. Création d'une service offer"
CREATE_RESPONSE=$(curl -s -X POST http://127.0.0.1:8000/api/service-offers \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "title": "Test Service Offer",
    "description": "Test description",
    "price": 100,
    "categories": ["test"],
    "execution_time": "1 week",
    "concepts": "Test concepts",
    "revisions": "3",
    "status": "published"
  }')

echo "Réponse de création: $CREATE_RESPONSE"

# Extraire l'ID de la service offer créée
OFFER_ID=$(echo $CREATE_RESPONSE | grep -o '"id":[0-9]*' | cut -d: -f2)
echo "ID de l'offre créée: $OFFER_ID"

if [ ! -z "$OFFER_ID" ]; then
    # 4. Tester la suppression
    echo ""
    echo "4. Test de suppression de l'offre ID $OFFER_ID"
    DELETE_RESPONSE=$(curl -s -w "HTTP_STATUS:%{http_code}" -X DELETE http://127.0.0.1:8000/api/service-offers/$OFFER_ID \
      -H "Authorization: Bearer $TOKEN" \
      -H "Accept: application/json")
    
    HTTP_STATUS=$(echo $DELETE_RESPONSE | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
    RESPONSE_BODY=$(echo $DELETE_RESPONSE | sed 's/HTTP_STATUS:[0-9]*$//')
    
    echo "Status Code: $HTTP_STATUS"
    echo "Réponse: $RESPONSE_BODY"
    
    if [ "$HTTP_STATUS" = "204" ] || [ "$HTTP_STATUS" = "200" ]; then
        echo "✅ SUCCÈS: La suppression a fonctionné!"
    elif [ "$HTTP_STATUS" = "403" ]; then
        echo "❌ ÉCHEC: Accès refusé - le problème persiste"
        echo "Vérifiez les logs dans storage/logs/laravel.log"
    elif [ "$HTTP_STATUS" = "401" ]; then
        echo "❌ ÉCHEC: Non authentifié - problème d'authentification"
    else
        echo "❌ ÉCHEC: Status inattendu $HTTP_STATUS"
    fi
else
    echo "❌ ERREUR: Impossible de créer une service offer"
fi

echo ""
echo "=== FIN DU TEST ==="
