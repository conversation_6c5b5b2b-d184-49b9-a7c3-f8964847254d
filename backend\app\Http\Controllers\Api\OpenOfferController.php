<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Models\OpenOffer;
use Illuminate\Http\Request;
use App\Models\OfferApplication;
use Illuminate\Http\JsonResponse; // Import ProfessionalProfile model
use App\Models\ProfessionalProfile; // Use the new Request class
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StoreOpenOfferRequest;
use Illuminate\Support\Facades\Notification; // Import the notification class
use App\Notifications\NewOpenOfferNotification;
use App\Notifications\DirectOfferInvitationNotification;


class OpenOfferController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        try {
            // Modify the query to exclude 'closed' and 'completed' offers
            $openOffers = OpenOffer::with('user')
                ->whereNotIn('status', ['closed', 'completed'])
                ->get();
            return response()->json(['open_offers' => $openOffers]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération de la liste des offres ouvertes: ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la récupération des offres ouvertes.'], 500);
        }
    }


    /**
     * Store a newly created resource in storage with dynamic filters.
     */
    public function store(StoreOpenOfferRequest $request): JsonResponse
    {
        $validatedData = $request->validated();

        try {

            $filters = $validatedData['filters'] ?? [];

            $openOffer = new OpenOffer();
            $openOffer->user_id = $request->user()->id;
            $openOffer->title = $validatedData['title'];
            // Stocker les compétences dans la colonne categories au lieu des catégories
            $openOffer->categories = isset($filters['skills']) ? $filters['skills'] : null;
            $openOffer->budget = $validatedData['budget'] ?? null;
            $openOffer->deadline = $validatedData['deadline'] ?? null;
            $openOffer->company = $validatedData['company'] ?? null;
            $openOffer->website = $validatedData['website'] ?? null;
            $openOffer->description = $validatedData['description'];
            $offerData = collect($validatedData)->except('filters', 'files', 'status')->toArray(); // Exclure 'filters', 'files' et 'status' pour mass assignment
            $openOffer->fill($offerData);
            $openOffer->status = 'pending'; // Définir le statut initial à "pending"
            // Gestion des fichiers (inchangé)
            $filePaths = [];
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    try {
                        $path = $file->store('offer_files', 'public');
                        $filePaths[] = ['path' => $path, 'original_name' => $file->getClientOriginalName()];
                    } catch (\Exception $e) {
                        Log::error('Erreur lors de l\'upload du fichier: ' . $e->getMessage());
                        return response()->json(['message' => 'Erreur lors de l\'upload d\'un fichier.'], 500);
                    }
                }
                $openOffer->files = json_encode($filePaths);
            } else {
                $openOffer->files = null;
            }
            // Nous utilisons déjà les compétences des filtres pour la colonne categories

            $openOffer->save();

            // ** Matching des professionnels et Diffusion **
            $query = ProfessionalProfile::query()->whereHas('user', function ($q) {
                $q->where('is_professional', true);
            });

            if (!empty($filters)) {
                if (isset($filters['languages'])) {
                    $query->where(function ($q) use ($filters) {
                        foreach ($filters['languages'] as $lang) {
                            $q->orWhereJsonContains('languages', $lang); // Ajuster selon la structure de votre colonne 'languages'
                        }
                    });
                }
                if (isset($filters['skills'])) {
                    $query->where(function ($q) use ($filters) {
                        foreach ($filters['skills'] as $skill) {
                            $q->orWhereJsonContains('skills', $skill); // Ajuster selon la structure de votre colonne 'skills'
                        }
                    });
                }
                if (isset($filters['location'])) {
                    $query->where('city', $filters['location']); // Ajuster selon le champ de localisation
                }
                if (isset($filters['experience'])) {
                    $query->where('experience', '>=', $filters['experience']); // Ajuster selon le champ d'expérience
                }
                if (isset($filters['availability_status'])) {
                    $query->where('availability_status', $filters['availability_status']);
                }
            }

            $eligibleProfessionals = $query->get();
            $eligibleProfessionalsCount = $eligibleProfessionals->count();

            //Log::info('Offre ouverte créée (ID: ' . $openOffer->id . '). Professionnels éligibles: ' . $eligibleProfessionalsCount);
            // Log des IDs des professionnels éligibles pour vérification
            //Log::info('IDs des professionnels éligibles: ' . $eligibleProfessionals->pluck('id')->implode(', '));
            // ** Diffusion réelle : Envoyer des notifications aux professionnels éligibles **
            // Get the User models associated with the eligible ProfessionalProfiles
            $eligibleUsers = $eligibleProfessionals->pluck('user');

            // ** Save eligible users to the database **
            $openOffer->professionals()->attach($eligibleUsers->pluck('id')); // Attach users to the open offer


            // Send notifications to the User models
            Notification::send($eligibleUsers, new NewOpenOfferNotification($openOffer));

            // ** Diffusion réelle : Envoyer des notifications aux professionnels éligibles **
            //Notification::send($eligibleProfessionals, new NewOpenOfferNotification($openOffer));

            // ** Transition de statut: pending -> open après la diffusion **
            $openOffer->status = 'open';
            $openOffer->save();


            return response()->json([
                'open_offer' => $openOffer,
                'eligible_professionals_count' => $eligibleProfessionalsCount,
                'message' => 'Offre ouverte créée et diffusée avec succès. ' . $eligibleProfessionalsCount . ' professionnels éligibles ont été notifiés.',
            ], 201);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la création et diffusion de l\'offre ouverte: ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la création de l\'offre ouverte.'], 500);
        }
    }


    /**
     * Display the specified resource.
     */
    public function show(OpenOffer $openOffer): JsonResponse
    {
        try {
            $openOffer->increment('views_count');
            return response()->json(['open_offer' => $openOffer->load('user', 'applications.freelanceProfile.user')]); // Keep the relation name for now as it's defined in the OfferApplication model
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'affichage de l\'offre ouverte ID ' . $openOffer->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la récupération de l\'offre ouverte.'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OpenOffer $openOffer): JsonResponse
    {
        if ($openOffer->user_id !== auth()->id()) {
            return response()->json(['message' => 'Non autorisé.'], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'string|max:255',
            'categories' => 'nullable|array',
            'categories.*' => 'string|max:255',
            'budget' => 'nullable|string|max:255',
            'deadline' => 'nullable|date',
            'company' => 'nullable|string|max:255',
            'website' => 'nullable|url|max:255',
            'description' => 'string',
            'files' => 'nullable|array',
            'files.*' => 'file|max:2048',
            'recruitment_type' => 'in:company,personal',
            'open_to_applications' => 'boolean',
            'auto_invite' => 'boolean',
            // 'status' => 'in:open,closed,in_progress,completed,pending', // Ne pas permettre de changer le statut directement via update
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $validatedData = $validator->validated();

            // Gestion des fichiers pour la mise à jour (similaire à la création)
            $filePaths = [];
            if ($request->hasFile('files')) {
                // Optionnel: Supprimer les anciens fichiers si nécessaire avant d'uploader les nouveaux
                // Storage::disk('public')->delete($openOffer->files); // Exemple de suppression (adapter selon la structure de stockage)

                foreach ($request->file('files') as $file) {
                    try {
                        $path = $file->store('offer_files', 'public');
                        $filePaths[] = ['path' => $path, 'original_name' => $file->getClientOriginalName()];
                    } catch (\Exception $e) {
                        Log::error('Erreur lors de l\'upload du fichier (mise à jour): ' . $e->getMessage());
                        return response()->json(['message' => 'Erreur lors de l\'upload d\'un fichier pendant la mise à jour.'], 500);
                    }
                }
                $validatedData['files'] = json_encode($filePaths);
            }

            // Stocker les compétences dans la colonne categories au lieu des catégories
            if (isset($validatedData['filters']) && isset($validatedData['filters']['skills'])) {
                $validatedData['categories'] = $validatedData['filters']['skills'];
            }

            $openOffer->update($validatedData);
            return response()->json(['open_offer' => $openOffer, 'message' => 'Offre ouverte mise à jour avec succès.']);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la mise à jour de l\'offre ouverte ID ' . $openOffer->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la mise à jour de l\'offre ouverte.'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OpenOffer $openOffer): JsonResponse
    {
        if ($openOffer->user_id !== auth()->id()) {
            return response()->json(['message' => 'Non autorisé.'], 403);
        }
        // Optionnel: Supprimer les fichiers associés à l'offre avant de supprimer l'offre elle-même
        // Storage::disk('public')->delete($openOffer->files); // Adapter selon la structure de stockage
        try {
            $openOffer->delete();
            return response()->json(['message' => 'Offre ouverte supprimée avec succès.']);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la suppression de l\'offre ouverte ID ' . $openOffer->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la suppression de l\'offre ouverte.'], 500);
        }
    }

    /**
     * Apply to an open offer or respond to an invitation.
     */
    public function apply(Request $request, OpenOffer $openOffer): JsonResponse
    {
        if ($openOffer->status !== 'open') {
            return response()->json(['message' => 'This offer is no longer open for applications.'], 400);
        }

        $validator = Validator::make($request->all(), ['proposal' => 'nullable|string']);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();
        if (!$user->is_professional || !$user->professionalProfile) {
            return response()->json(['message' => 'Seuls les professionnels avec un profil professionnel peuvent postuler.'], 403);
        }

        try {
            $existingInvitedApplication = OfferApplication::where('open_offer_id', $openOffer->id)
                ->where('professional_profile_id', $user->professionalProfile->id)
                ->where('status', 'invited')
                ->first(); // Use first() to get the application if it exists

            if ($existingInvitedApplication) {
                // Professional is responding to an invitation, update the existing application
                try {
                    $existingInvitedApplication->status = 'pending'; // Or 'applied', or 'accepted' based on your workflow
                    $existingInvitedApplication->proposal = $validator->validated()['proposal'] ?? $existingInvitedApplication->proposal; // Keep existing proposal or update if provided
                    $existingInvitedApplication->save();

                    return response()->json(['application' => $existingInvitedApplication, 'message' => 'Invitation acceptée et candidature soumise avec succès.'], 200);
                } catch (\Exception $e) {
                    Log::error('Erreur lors de la mise à jour de la candidature invitée: ' . $e->getMessage());
                    return response()->json(['message' => 'Erreur serveur lors de la mise à jour de la candidature. Veuillez réessayer plus tard.'], 500);
                }
            } else {
                // Professional is applying normally, check for existing applications (excluding invited)
                $existingApplication = OfferApplication::where('open_offer_id', $openOffer->id)
                    ->where('professional_profile_id', $user->professionalProfile->id)
                    ->whereNotIn('status', ['invited'])
                    ->exists();

                if ($existingApplication) {
                    return response()->json(['message' => 'Vous avez déjà postulé à cette offre.'], 409);
                }

                // Create a new application
                try {
                    $application = OfferApplication::create([
                        'open_offer_id' => $openOffer->id,
                        'professional_profile_id' => $user->professionalProfile->id,
                        'proposal' => $validator->validated()['proposal'] ?? null,
                    ]);

                    return response()->json(['application' => $application, 'message' => 'Candidature soumise avec succès.'], 201);
                } catch (\Exception $e) {
                    Log::error('Erreur lors de la soumission de la candidature: ' . $e->getMessage());
                    return response()->json(['message' => 'Erreur serveur lors de la soumission de la candidature. Veuillez réessayer plus tard.'], 500);
                }
            }
        } catch (\Exception $e) {
            Log::error('Erreur lors du processus de candidature à l\'offre ouverte ID ' . $openOffer->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la soumission de la candidature.'], 500);
        }
    }

    /**
     * List applications for a specific open offer (for the offer creator).
     */
    public function applications(OpenOffer $openOffer): JsonResponse
    {
        if ($openOffer->user_id !== auth()->id()) {
            return response()->json(['message' => 'Non autorisé à voir les candidatures.'], 403);
        }
        try {
            $applications = $openOffer->applications()->with('freelanceProfile.user')->get(); // Keep the relation name for now as it's defined in the OfferApplication model
            return response()->json(['applications' => $applications]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des candidatures pour l\'offre ouverte ID ' . $openOffer->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la récupération des candidatures.'], 500);
        }
    }

    /**
     * List accepted applications for a specific open offer (for assignment selection).
     */
    public function acceptedApplications(OpenOffer $openOffer): JsonResponse
    {
        if ($openOffer->user_id !== auth()->id()) {
            return response()->json(['message' => 'Non autorisé à voir les candidatures.'], 403);
        }

        try {
            $acceptedApplications = $openOffer->applications()
                ->where('status', 'accepted')
                ->with('freelanceProfile.user')
                ->get();

            return response()->json(['accepted_applications' => $acceptedApplications]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des candidatures acceptées pour l\'offre ouverte ID ' . $openOffer->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la récupération des candidatures acceptées.'], 500);
        }
    }

    /**
     * Accept or reject an application (without affecting the offer status).
     */
    public function updateApplicationStatus(Request $request, OfferApplication $application): JsonResponse
    {
        $openOffer = $application->openOffer;
        if ($openOffer->user_id !== auth()->id()) {
            return response()->json(['message' => 'Non autorisé à modifier le statut de la candidature.'], 403);
        }

        if ($openOffer->status !== 'open') {
            return response()->json(['message' => 'Le statut de l\'offre doit être "open" pour modifier les candidatures.'], 400);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:accepted,rejected',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $validatedData = $validator->validated();
            $application->update($validatedData);

            return response()->json([
                'application' => $application,
                'message' => 'Statut de la candidature mis à jour avec succès.'
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la mise à jour du statut de la candidature ID ' . $application->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la mise à jour du statut de la candidature.'], 500);
        }
    }

    /**
     * Assign the offer to a chosen professional and transition offer status to in_progress.
     */
    public function assignOfferToProfessional(Request $request, OpenOffer $openOffer): JsonResponse
    {
        if ($openOffer->user_id !== auth()->id()) {
            return response()->json(['message' => 'Non autorisé à attribuer cette offre.'], 403);
        }

        if ($openOffer->status !== 'open') {
            return response()->json(['message' => 'L\'offre doit être en statut "open" pour être attribuée.'], 400);
        }

        $validator = Validator::make($request->all(), [
            'application_id' => 'required|exists:offer_applications,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Vérifier que la candidature appartient bien à cette offre
            $chosenApplication = OfferApplication::where('id', $request->application_id)
                ->where('open_offer_id', $openOffer->id)
                ->first();

            if (!$chosenApplication) {
                return response()->json(['message' => 'La candidature spécifiée n\'appartient pas à cette offre.'], 400);
            }

            // Vérifier que la candidature est acceptée
            if ($chosenApplication->status !== 'accepted') {
                return response()->json(['message' => 'Seules les candidatures acceptées peuvent être attribuées.'], 400);
            }

            // Passer l'offre en statut "in_progress"
            $openOffer->status = 'in_progress';
            $openOffer->save();

            // Rejeter automatiquement toutes les autres candidatures acceptées
            OfferApplication::where('open_offer_id', $openOffer->id)
                ->where('id', '!=', $chosenApplication->id)
                ->whereIn('status', ['accepted', 'pending', 'invited'])
                ->update(['status' => 'rejected']);

            // Recharger l'offre avec ses relations
            $openOffer->load(['applications.freelanceProfile.user']);

            return response()->json([
                'open_offer' => $openOffer,
                'assigned_application' => $chosenApplication,
                'message' => 'Offre attribuée avec succès au professionnel choisi.'
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'attribution de l\'offre ID ' . $openOffer->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de l\'attribution de l\'offre.'], 500);
        }
    }

    /**
     * Close the specified open offer. Can be closed from 'open' or 'in_progress' status.
     */
    public function close(OpenOffer $openOffer): JsonResponse
    {
        if ($openOffer->user_id !== auth()->id()) {
            return response()->json(['message' => 'Non autorisé à clôturer cette offre.'], 403);
        }

        if ($openOffer->status === 'closed' || $openOffer->status === 'completed') {
            return response()->json(['message' => 'Cette offre est déjà clôturée ou complétée.'], 400);
        }

        try {
            $openOffer->status = 'closed';
            $openOffer->save();

            return response()->json(['open_offer' => $openOffer, 'message' => 'Offre ouverte clôturée avec succès.']);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la clôture de l\'offre ouverte ID ' . $openOffer->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la clôture de l\'offre ouverte.'], 500);
        }
    }

    /**
     * Mark the specified open offer as completed. Must be in 'in_progress' status.
     */
    public function complete(OpenOffer $openOffer): JsonResponse
    {
        if ($openOffer->user_id !== auth()->id()) {
            return response()->json(['message' => 'Non autorisé à compléter cette offre.'], 403);
        }

        if ($openOffer->status !== 'in_progress') {
            return response()->json(['message' => 'L\'offre doit être en statut "in_progress" pour être marquée comme complétée.'], 400);
        }

        try {
            $openOffer->status = 'completed';
            $openOffer->save();

            return response()->json(['open_offer' => $openOffer, 'message' => 'Offre ouverte marquée comme complétée avec succès.']);
        } catch (\Exception $e) {
            Log::error('Erreur lors du marquage comme complété de l\'offre ouverte ID ' . $openOffer->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors du marquage comme complété de l\'offre ouverte.'], 500);
        }
    }

    /**
     * Invite a professional directly to an open offer.
     */
    public function inviteProfessional(Request $request, OpenOffer $openOffer): JsonResponse
    {
        if ($openOffer->user_id !== auth()->id()) {
            return response()->json(['message' => 'Non autorisé à inviter des professionnels pour cette offre.'], 403);
        }

        if ($openOffer->status !== 'open' && $openOffer->status !== 'pending') { // Allow invitation for 'pending' and 'open' offers
            return response()->json(['message' => 'Les invitations ne peuvent être envoyées que pour les offres en statut "pending" ou "open".'], 400);
        }

        $validator = Validator::make($request->all(), [
            'professional_id' => 'required|exists:users,id,is_professional,1', // Ensure it's a professional user
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $professionalUser = User::find($request->professional_id);
        if (!$professionalUser->professionalProfile) {
            return response()->json(['message' => 'Le professionnel invité n\'a pas de profil professionnel.'], 422);
        }

        try {
            // Check if already invited or applied
            $existingInvitation = OfferApplication::where('open_offer_id', $openOffer->id)
                ->where('professional_profile_id', $professionalUser->professionalProfile->id)
                ->whereIn('status', ['pending', 'accepted', 'invited']) // Check for existing pending, accepted or invited applications/invitations
                ->exists();

            if ($existingInvitation) {
                return response()->json(['message' => 'Ce professionnel a déjà été invité ou a déjà postulé à cette offre.'], 409);
            }

            // Create invitation as an OfferApplication with 'invited' status
            $invitation = OfferApplication::create([
                'open_offer_id' => $openOffer->id,
                'professional_profile_id' => $professionalUser->professionalProfile->id,
                'status' => 'invited', // Status to indicate it's an invitation
            ]);
            // Send notification to the invited professional
            // Notification::send($professionalUser, new DirectOfferInvitationNotification($openOffer, auth()->user())); // Original line - sending to User, should be fine here if $professionalUser is indeed a User model

            // No change needed here if $professionalUser is already a User model.
            // However, double check that $professionalUser is of type User and not ProfessionalProfile.
            // In your code, you are using User::find($request->professional_id), which is correct to get a User model.
            Notification::send($professionalUser, new DirectOfferInvitationNotification($openOffer, auth()->user()));

            // Send notification to the invited professional
            //Notification::send($professionalUser, new DirectOfferInvitationNotification($openOffer, auth()->user())); // Pass the client user as well

            return response()->json([
                'invitation' => $invitation,
                'message' => 'Professionnel invité avec succès à l\'offre.',
            ], 201);
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'invitation du professionnel ID ' . $request->professional_id . ' à l\'offre ouverte ID ' . $openOffer->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de l\'invitation du professionnel.'], 500);
        }
    }


    /**
     * Get attributed open offers for a specific professional.
     *
     * @param  int  $professionalId
     * @return JsonResponse
     */
    public function getAttributedOffersForProfessional(int $professionalId): JsonResponse
    {
        $professionalUser = User::find($professionalId);

        if (!$professionalUser || !$professionalUser->is_professional) {
            return response()->json(['message' => 'Professional not found.'], 404);
        }

        try {
            // Load the attributedOpenOffers relationship
            $attributedOffers = $professionalUser->attributedOpenOffers()->with('user')->get();

            return response()->json(['attributed_open_offers' => $attributedOffers]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des offres attribuées pour le professionnel ID ' . $professionalId . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la récupération des offres attribuées.'], 500);
        }
    }

    /**
     * Reject an application for an open offer by a professional.
     *
     * @param  Request  $request
     * @param  OpenOffer $openOffer
     * @return JsonResponse
     */
    public function rejectOffer(Request $request, OpenOffer $openOffer): JsonResponse
    {
        $user = $request->user();

        if (!$user->is_professional || !$user->professionalProfile) {
            return response()->json(['message' => 'Seuls les professionnels peuvent refuser une offre.'], 403);
        }

        try {
            // Check if an application exists for this professional and offer
            $application = OfferApplication::where('open_offer_id', $openOffer->id)
                ->where('professional_profile_id', $user->professionalProfile->id)
                ->first();

            if (!$application) {
                return response()->json(['message' => 'Vous n\'avez pas postulé à cette offre ou vous n\'avez pas été invité.'], 404);
            }

            // Check if the application is already accepted or rejected by client
            if ($application->status === 'accepted' || $application->status === 'rejected') {
                return response()->json(['message' => 'Vous ne pouvez pas refuser une candidature déjà acceptée ou rejetée par le client.'], 400);
            }
            // Check if the application is already rejected by professional
            if ($application->status === 'rejected') {
                return response()->json(['message' => 'Vous avez déjà refusé cette offre.'], 409);
            }

            $application->status = 'rejected';
            $application->save();

            return response()->json(['application' => $application, 'message' => 'Offre refusée avec succès.'], 200);
        } catch (\Exception $e) {
            Log::error('Erreur lors du refus de l\'offre ouverte ID ' . $openOffer->id . ' par le professionnel ID ' . $user->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors du refus de l\'offre.'], 500);
        }
    }

    /**
     * Get all open offers for the authenticated client.
     *
     * @return JsonResponse
     */
    public function getClientOpenOffers(): JsonResponse
    {
        $client = auth()->user();

        if (!$client) {
            return response()->json(['message' => 'Client non authentifié.'], 401); // Or handle unauthenticated user appropriately
        }

        if ($client->is_professional) {
            return response()->json(['message' => 'Les professionnels n\'ont pas accès à cette fonctionnalité.'], 403); // Or appropriate message
        }

        try {
            $openOffers = OpenOffer::with('applications', 'applications.freelanceProfile.user') // Keep the relation name for now as it's defined in the OfferApplication model
                ->where('user_id', $client->id)
                ->whereNotIn('status', ['closed', 'completed']) // Exclude closed and completed offers
                ->latest()
                ->get();

            return response()->json(['client_open_offers' => $openOffers]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des offres ouvertes du client ID ' . $client->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la récupération des offres ouvertes du client.'], 500);
        }
    }

    public function getClientInProgressOffers(): JsonResponse
    {
        $client = auth()->user();

        if (!$client) {
            return response()->json(['message' => 'Client non authentifié.'], 401); // Or handle unauthenticated user appropriately
        }

        if ($client->is_professional) {
            return response()->json(['message' => 'Les professionnels n\'ont pas accès à cette fonctionnalité.'], 403); // Or appropriate message
        }

        try {
            $openOffers = OpenOffer::with('applications', 'applications.freelanceProfile.user') // Keep the relation name for now as it's defined in the OfferApplication model
                ->where('user_id', $client->id)
                ->whereNotIn('status', ['pending', 'open', 'closed', 'completed']) // Exclude closed and completed offers
                ->latest()
                ->get();

            return response()->json(['offers' => $openOffers]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des offres ouvertes du client ID ' . $client->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la récupération des offres ouvertes du client.'], 500);
        }
    }

    public function getClientPendingOffers(): JsonResponse
    {
        $client = auth()->user();

        if (!$client) {
            return response()->json(['message' => 'Client non authentifié.'], 401); // Or handle unauthenticated user appropriately
        }

        if ($client->is_professional) {
            return response()->json(['message' => 'Les professionnels n\'ont pas accès à cette fonctionnalité.'], 403); // Or appropriate message
        }

        try {
            $openOffers = OpenOffer::with('applications', 'applications.freelanceProfile.user') // Keep the relation name for now as it's defined in the OfferApplication model
                ->where('user_id', $client->id)
                ->whereNotIn('status', ['in_progress', 'closed', 'completed']) // Exclude closed and completed offers
                ->latest()
                ->get();

            return response()->json(['offers' => $openOffers]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des offres ouvertes du client ID ' . $client->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la récupération des offres ouvertes du client.'], 500);
        }
    }

    public function getClientClosedOrCompleteOffers(): JsonResponse
    {
        $client = auth()->user();

        if (!$client) {
            return response()->json(['message' => 'Client non authentifié.'], 401); // Or handle unauthenticated user appropriately
        }
        if ($client->is_professional) {
            return response()->json(['message' => 'Les professionnels n\'ont pas accès à cette fonctionnalité.'], 403); // Or appropriate message
        }

        try {
            $closedCompletedOffers = OpenOffer::with('applications', 'applications.freelanceProfile.user') // Keep the relation name for now as it's defined in the OfferApplication model
                ->where('user_id', $client->id)
                ->whereIn('status', ['closed', 'completed']) // Include only closed and completed offers
                ->latest()
                ->get();

            return response()->json(['offers' => $closedCompletedOffers]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des offres clôturées/complétées du client ID ' . $client->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la récupération des offres clôturées/complétées du client.'], 500);
        }
    }

    /**
     * Get all closed or completed offers for the authenticated client.
     *
     * @return JsonResponse
     */
    public function getClientClosedCompletedOffers(): JsonResponse
    {
        $client = auth()->user();

        if (!$client) {
            return response()->json(['message' => 'Client non authentifié.'], 401); // Or handle unauthenticated user appropriately
        }
        if ($client->is_professional) {
            return response()->json(['message' => 'Les professionnels n\'ont pas accès à cette fonctionnalité.'], 403); // Or appropriate message
        }

        try {
            $closedCompletedOffers = OpenOffer::with('applications', 'applications.freelanceProfile.user') // Keep the relation name for now as it's defined in the OfferApplication model
                ->where('user_id', $client->id)
                ->whereIn('status', ['closed', 'completed']) // Include only closed and completed offers
                ->latest()
                ->get();

            return response()->json(['client_closed_completed_offers' => $closedCompletedOffers]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des offres clôturées/complétées du client ID ' . $client->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la récupération des offres clôturées/complétées du client.'], 500);
        }
    }
}
