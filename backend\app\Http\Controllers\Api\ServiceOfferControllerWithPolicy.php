<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ServiceOffer;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ServiceOfferControllerWithPolicy extends Controller
{
    /**
     * Remove the resource from storage using Policy.
     */
    public function destroy(ServiceOffer $serviceOffer): JsonResponse
    {
        try {
            // Utiliser la policy pour vérifier l'autorisation
            $this->authorize('delete', $serviceOffer);

            // Log des informations de débogage
            Log::info('ServiceOffer destroy - Authorized deletion:', [
                'service_offer_id' => $serviceOffer->id,
                'user_id' => auth()->id(),
            ]);

            // Supprimer les fichiers associés s'ils existent
            if ($serviceOffer->files && is_array($serviceOffer->files)) {
                foreach ($serviceOffer->files as $file) {
                    if (isset($file['path'])) {
                        Storage::disk('public')->delete($file['path']);
                    }
                }
            }

            $serviceOffer->delete();
            Log::info('ServiceOffer destroy - Successfully deleted service offer ID: ' . $serviceOffer->id);
            return response()->json(['message' => 'Offre de service supprimée avec succès.'], 204);
        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            Log::warning('ServiceOffer destroy - Authorization failed:', [
                'service_offer_id' => $serviceOffer->id,
                'user_id' => auth()->id(),
                'message' => $e->getMessage()
            ]);
            return response()->json(['message' => 'Non autorisé à supprimer cette offre de service.'], 403);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la suppression de l\'offre de service ID ' . $serviceOffer->id . ': ' . $e->getMessage());
            return response()->json(['message' => 'Erreur lors de la suppression de l\'offre de service.'], 500);
        }
    }
}
